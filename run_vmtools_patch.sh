#!/bin/bash

# VMTools Patch Script Runner
# This script demonstrates how to use the improved VMTools patch script
# and provides various usage examples

set -euo pipefail

# Script configuration
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
readonly ORIGINAL_SCRIPT="$SCRIPT_DIR/fc_vmtools_patch.sh"
readonly IMPROVED_SCRIPT="$SCRIPT_DIR/fc_vmtools_patch_improved.sh"
readonly TEST_SCRIPT="$SCRIPT_DIR/test_vmtools_patch.sh"
readonly CONFIG_FILE="$SCRIPT_DIR/fc_vmtools_patch.conf"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# Logging function
log_msg() {
    local level="$1"
    local msg="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case "$level" in
        INFO)
            echo -e "${BLUE}[INFO]${NC} $msg"
            ;;
        SUCCESS)
            echo -e "${GREEN}[SUCCESS]${NC} $msg"
            ;;
        WARNING)
            echo -e "${YELLOW}[WARNING]${NC} $msg"
            ;;
        ERROR)
            echo -e "${RED}[ERROR]${NC} $msg"
            ;;
    esac
}

# Display usage information
usage() {
    cat << EOF
VMTools Patch Script Runner

USAGE:
    $0 [COMMAND] [OPTIONS]

COMMANDS:
    analyze         Analyze the original script for issues
    test           Run the test suite
    demo           Run demonstration scenarios
    compare        Compare original vs improved script
    help           Show this help message

OPTIONS:
    --config FILE  Specify configuration file (default: ./fc_vmtools_patch.conf)
    --verbose      Enable verbose output
    --dry-run      Show what would be done without making changes

EXAMPLES:
    $0 analyze                    # Analyze original script
    $0 test                       # Run all tests
    $0 demo --dry-run            # Run demo in dry-run mode
    $0 compare                   # Compare scripts

EOF
}

# Analyze the original script
analyze_original_script() {
    log_msg INFO "Analyzing original VMTools patch script..."
    
    if [[ ! -f "$ORIGINAL_SCRIPT" ]]; then
        log_msg ERROR "Original script not found: $ORIGINAL_SCRIPT"
        return 1
    fi
    
    echo ""
    echo "🔍 SCRIPT ANALYSIS RESULTS"
    echo "=========================="
    
    # Check for critical issues
    local issues_found=0
    
    # Check for disabled strict mode
    if grep -q "^# set -euo pipefail" "$ORIGINAL_SCRIPT"; then
        echo -e "${RED}❌ CRITICAL: Strict mode is disabled${NC}"
        ((issues_found++))
    fi
    
    # Check for insecure temp directories
    if grep -q "TMP_DIR.*\$\$" "$ORIGINAL_SCRIPT"; then
        echo -e "${RED}❌ SECURITY: Insecure temporary directory creation${NC}"
        ((issues_found++))
    fi
    
    # Check for sudo usage without proper validation
    local sudo_count=$(grep -c "sudo" "$ORIGINAL_SCRIPT" || echo 0)
    if [[ $sudo_count -gt 5 ]]; then
        echo -e "${YELLOW}⚠️  WARNING: Excessive sudo usage ($sudo_count instances)${NC}"
        ((issues_found++))
    fi
    
    # Check for error handling
    if ! grep -q "trap.*cleanup" "$ORIGINAL_SCRIPT"; then
        echo -e "${RED}❌ RELIABILITY: No cleanup trap handlers${NC}"
        ((issues_found++))
    fi
    
    # Check for lock mechanism
    if ! grep -q "LOCK_FILE\|lock" "$ORIGINAL_SCRIPT"; then
        echo -e "${YELLOW}⚠️  WARNING: No concurrent execution protection${NC}"
        ((issues_found++))
    fi
    
    # Summary
    echo ""
    if [[ $issues_found -eq 0 ]]; then
        echo -e "${GREEN}✅ No major issues found${NC}"
    else
        echo -e "${RED}Found $issues_found potential issues${NC}"
        echo "See DESIGN_DOCUMENT.md for detailed analysis and solutions"
    fi
    
    echo ""
    log_msg INFO "Analysis completed. Check DESIGN_DOCUMENT.md for detailed recommendations."
}

# Run the test suite
run_tests() {
    log_msg INFO "Running VMTools patch script test suite..."
    
    if [[ ! -f "$TEST_SCRIPT" ]]; then
        log_msg ERROR "Test script not found: $TEST_SCRIPT"
        return 1
    fi
    
    # Make test script executable
    chmod +x "$TEST_SCRIPT"
    
    # Run tests
    if bash "$TEST_SCRIPT"; then
        log_msg SUCCESS "All tests passed!"
        return 0
    else
        log_msg ERROR "Some tests failed. Check test_results.log for details."
        return 1
    fi
}

# Run demonstration scenarios
run_demo() {
    local dry_run="${1:-0}"
    
    log_msg INFO "Running VMTools patch script demonstration..."
    
    if [[ ! -f "$IMPROVED_SCRIPT" ]]; then
        log_msg ERROR "Improved script not found: $IMPROVED_SCRIPT"
        return 1
    fi
    
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_msg ERROR "Configuration file not found: $CONFIG_FILE"
        return 1
    fi
    
    # Make script executable
    chmod +x "$IMPROVED_SCRIPT"
    
    echo ""
    echo "🚀 DEMONSTRATION SCENARIOS"
    echo "========================="
    
    # Scenario 1: Show help
    echo ""
    echo "1. Displaying help information:"
    echo "   Command: $IMPROVED_SCRIPT --help"
    if bash "$IMPROVED_SCRIPT" --help 2>/dev/null; then
        log_msg SUCCESS "Help display works correctly"
    else
        log_msg WARNING "Help display may have issues"
    fi
    
    # Scenario 2: Show version
    echo ""
    echo "2. Displaying version information:"
    echo "   Command: $IMPROVED_SCRIPT --version"
    if bash "$IMPROVED_SCRIPT" --version 2>/dev/null; then
        log_msg SUCCESS "Version display works correctly"
    else
        log_msg WARNING "Version display may have issues"
    fi
    
    # Scenario 3: Configuration validation
    echo ""
    echo "3. Configuration validation:"
    echo "   Config file: $CONFIG_FILE"
    if [[ -f "$CONFIG_FILE" ]]; then
        log_msg SUCCESS "Configuration file exists"
        echo "   Sample configuration:"
        head -10 "$CONFIG_FILE" | sed 's/^/   /'
    else
        log_msg ERROR "Configuration file not found"
    fi
    
    # Scenario 4: Dry run mode
    if [[ "$dry_run" == "1" ]]; then
        echo ""
        echo "4. Dry run mode demonstration:"
        echo "   Command: $IMPROVED_SCRIPT --dry-run --config $CONFIG_FILE"
        log_msg INFO "This would show what the script would do without making changes"
        log_msg WARNING "Skipping actual dry run as it requires proper ISO files"
    fi
    
    echo ""
    log_msg INFO "Demonstration completed. Use actual ISO files for full testing."
}

# Compare original vs improved script
compare_scripts() {
    log_msg INFO "Comparing original vs improved VMTools patch script..."
    
    if [[ ! -f "$ORIGINAL_SCRIPT" ]]; then
        log_msg ERROR "Original script not found: $ORIGINAL_SCRIPT"
        return 1
    fi
    
    if [[ ! -f "$IMPROVED_SCRIPT" ]]; then
        log_msg ERROR "Improved script not found: $IMPROVED_SCRIPT"
        return 1
    fi
    
    echo ""
    echo "📊 SCRIPT COMPARISON"
    echo "==================="
    
    # File sizes
    local orig_size=$(wc -l < "$ORIGINAL_SCRIPT")
    local impr_size=$(wc -l < "$IMPROVED_SCRIPT")
    
    echo "File sizes:"
    echo "  Original: $orig_size lines"
    echo "  Improved: $impr_size lines"
    
    # Key improvements
    echo ""
    echo "Key improvements in the new version:"
    echo "✅ Strict mode enabled (set -euo pipefail)"
    echo "✅ Secure temporary directory creation with mktemp"
    echo "✅ File-based locking mechanism"
    echo "✅ Enhanced error handling with retry and backoff"
    echo "✅ Structured logging with timestamps and PIDs"
    echo "✅ Proper signal handling and cleanup"
    echo "✅ Configuration validation"
    echo "✅ Comprehensive test suite"
    
    # Security improvements
    echo ""
    echo "Security enhancements:"
    echo "🔒 Secure temporary file creation"
    echo "🔒 Proper permission handling (chmod 700)"
    echo "🔒 Input validation and sanitization"
    echo "🔒 Reduced sudo usage with validation"
    
    # Reliability improvements
    echo ""
    echo "Reliability enhancements:"
    echo "🛡️  Concurrent execution protection"
    echo "🛡️  Resource cleanup on all exit paths"
    echo "🛡️  Retry mechanisms with exponential backoff"
    echo "🛡️  Comprehensive error handling"
    
    echo ""
    log_msg SUCCESS "Comparison completed. See DESIGN_DOCUMENT.md for detailed analysis."
}

# Main execution
main() {
    local command="${1:-help}"
    local dry_run=0
    local verbose=0
    local config_file="$CONFIG_FILE"
    
    # Parse options
    shift || true
    while [[ $# -gt 0 ]]; do
        case $1 in
            --config)
                config_file="$2"
                shift 2
                ;;
            --dry-run)
                dry_run=1
                shift
                ;;
            --verbose)
                verbose=1
                shift
                ;;
            *)
                log_msg ERROR "Unknown option: $1"
                usage
                exit 1
                ;;
        esac
    done
    
    # Execute command
    case "$command" in
        analyze)
            analyze_original_script
            ;;
        test)
            run_tests
            ;;
        demo)
            run_demo "$dry_run"
            ;;
        compare)
            compare_scripts
            ;;
        help|--help|-h)
            usage
            ;;
        *)
            log_msg ERROR "Unknown command: $command"
            usage
            exit 1
            ;;
    esac
}

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
