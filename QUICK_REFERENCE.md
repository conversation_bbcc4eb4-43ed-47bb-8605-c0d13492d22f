# VMTools补丁脚本快速参考指南

## 🚀 快速开始

### 1. 环境检查
```bash
# 检查必需工具
which mktemp mount umount sudo

# 检查Bash版本 (需要4.0+)
bash --version

# 检查磁盘空间
df -h /opt/vmtools
```

### 2. 配置文件设置
```bash
# 复制配置模板
cp fc_vmtools_patch.conf.template fc_vmtools_patch.conf

# 编辑配置文件
vim fc_vmtools_patch.conf
```

### 3. 基本使用
```bash
# 显示帮助
./fc_vmtools_patch_improved.sh --help

# 检查状态
./fc_vmtools_patch_improved.sh --status

# 干运行模式
./fc_vmtools_patch_improved.sh --dry-run

# 正常执行
./fc_vmtools_patch_improved.sh
```

## 📋 命令参考

### 主要选项

| 选项 | 描述 | 示例 |
|------|------|------|
| `-h, --help` | 显示帮助信息 | `./script.sh --help` |
| `-v, --version` | 显示版本信息 | `./script.sh --version` |
| `-d, --debug` | 启用调试模式 | `./script.sh --debug` |
| `-c, --config` | 指定配置文件 | `./script.sh --config /path/to/config` |
| `--dry-run` | 干运行模式 | `./script.sh --dry-run` |
| `--rollback` | 回滚到上一版本 | `./script.sh --rollback` |
| `--force` | 强制继续执行 | `./script.sh --force` |
| `--force-update` | 强制更新 | `./script.sh --force-update` |
| `--status` | 显示ISO状态 | `./script.sh --status` |

### 配置参数

| 参数 | 必需 | 描述 | 示例值 |
|------|------|------|--------|
| `ISO_SRC` | ✅ | 源ISO文件路径 | `./ds-vm-isp-20.0.2.3530.iso` |
| `SRC_DIR` | ✅ | 源目录名称 | `windows` |
| `DS_ISP_DIR` | ✅ | 目标目录名称 | `ds_isp` |
| `VMTOOLS_DIR` | ✅ | VMTools目录 | `/opt/patch/programfiles/vmtools` |
| `ISO_SUBDIR` | ✅ | ISO子目录 | `upgrade` |
| `LOG_FILE` | ❌ | 日志文件路径 | `/var/log/vmtools_patch.log` |
| `DEBUG` | ❌ | 调试模式 | `0` 或 `1` |
| `MAX_RETRIES` | ❌ | 最大重试次数 | `3` |
| `RETRY_DELAY` | ❌ | 重试延迟(秒) | `2` |

## 🔧 故障排除

### 常见问题

#### 1. 权限错误
```bash
# 错误信息
[ERROR] Cannot read source ISO: /path/to/source.iso

# 解决方案
sudo chown $USER:$USER /path/to/source.iso
chmod 644 /path/to/source.iso
```

#### 2. 磁盘空间不足
```bash
# 错误信息
[ERROR] Insufficient disk space detected

# 解决方案
# 清理临时文件
sudo rm -rf /tmp/vmtools_*
# 或使用强制模式
./script.sh --force
```

#### 3. 文件锁冲突
```bash
# 错误信息
[ERROR] Another instance is running

# 解决方案
# 检查锁文件
ls -la /var/lock/vmtools_patch.lock
# 如果确认没有其他实例，删除锁文件
sudo rm -f /var/lock/vmtools_patch.lock
```

#### 4. 挂载失败
```bash
# 错误信息
[ERROR] Failed to mount ISO

# 解决方案
# 检查ISO文件完整性
file /path/to/source.iso
# 检查挂载点
sudo umount /mnt/vmtools_* 2>/dev/null || true
```

### 日志分析

#### 日志级别
- `ERROR`: 严重错误，需要立即处理
- `WARNING`: 警告信息，可能影响功能
- `INFO`: 一般信息，正常操作记录
- `SUCCESS`: 成功操作记录
- `DEBUG`: 调试信息，仅在调试模式显示

#### 关键日志模式
```bash
# 查看错误日志
grep "ERROR" /var/log/vmtools_patch.log

# 查看最近的操作
tail -50 /var/log/vmtools_patch.log

# 查看特定时间段的日志
grep "2024-01-31" /var/log/vmtools_patch.log
```

## 🧪 测试指南

### 运行测试套件
```bash
# 运行所有测试
./test_vmtools_patch.sh

# 运行特定测试类别
./run_vmtools_patch.sh test

# 查看测试结果
cat test_results.log
```

### 手动测试步骤

#### 1. 功能测试
```bash
# 1. 检查帮助信息
./script.sh --help

# 2. 检查版本信息
./script.sh --version

# 3. 检查状态
./script.sh --status

# 4. 干运行测试
./script.sh --dry-run

# 5. 正常执行测试
./script.sh
```

#### 2. 错误处理测试
```bash
# 1. 测试无效配置
echo "INVALID_CONFIG=test" > test.conf
./script.sh --config test.conf

# 2. 测试权限错误
chmod 000 /path/to/source.iso
./script.sh

# 3. 测试磁盘空间不足
# (需要在磁盘空间不足的环境中测试)
```

## 📊 性能监控

### 关键指标

| 指标 | 正常范围 | 监控方法 |
|------|---------|----------|
| 执行时间 | < 30分钟 | 日志时间戳 |
| 内存使用 | < 1GB | `ps aux` |
| 磁盘使用 | < 1.5x ISO大小 | `du -sh` |
| CPU使用 | < 80% | `top` |

### 监控命令
```bash
# 监控脚本执行
watch -n 5 'ps aux | grep vmtools_patch'

# 监控磁盘使用
watch -n 10 'df -h /opt/vmtools'

# 监控内存使用
watch -n 5 'free -h'

# 监控日志
tail -f /var/log/vmtools_patch.log
```

## 🔒 安全检查清单

### 执行前检查
- [ ] 确认脚本来源可信
- [ ] 检查文件权限设置
- [ ] 验证配置文件内容
- [ ] 确认有足够的备份空间
- [ ] 检查sudo权限配置

### 执行中监控
- [ ] 监控资源使用情况
- [ ] 检查临时文件创建
- [ ] 验证权限变更
- [ ] 监控网络活动
- [ ] 检查系统日志

### 执行后验证
- [ ] 验证新ISO文件完整性
- [ ] 检查备份文件存在
- [ ] 确认临时文件已清理
- [ ] 验证权限恢复正常
- [ ] 检查审计日志

## 📚 相关文档

### 主要文档
- `TECHNICAL_DESIGN_SPEC.md` - 技术设计规范
- `ANALYSIS_REPORT.md` - 问题分析报告
- `DESIGN_DOCUMENT.md` - 设计文档

### 脚本文件
- `fc_vmtools_patch_improved.sh` - 改进版主脚本
- `test_vmtools_patch.sh` - 测试套件
- `run_vmtools_patch.sh` - 运行和分析工具
- `fc_vmtools_patch.conf` - 配置文件模板

### 日志文件
- `/var/log/vmtools_patch.log` - 主日志文件
- `test_results.log` - 测试结果日志
- `/var/log/syslog` - 系统日志

## 🆘 紧急联系

### 技术支持
- **开发团队**: <EMAIL>
- **运维团队**: <EMAIL>
- **安全团队**: <EMAIL>

### 紧急情况处理
1. **立即停止脚本执行**
   ```bash
   # 查找进程
   ps aux | grep vmtools_patch
   # 终止进程
   sudo kill -TERM <PID>
   ```

2. **执行紧急回滚**
   ```bash
   ./script.sh --rollback
   ```

3. **联系相关团队**
   - 技术问题: 开发团队
   - 环境问题: 运维团队
   - 安全问题: 安全团队

---

**快速参考版本**: v1.0  
**最后更新**: 2024-01-31  
**适用脚本版本**: v2.2-improved
