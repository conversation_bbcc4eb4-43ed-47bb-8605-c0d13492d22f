#!/bin/bash

# VMTools Patch Script - Improved Version
# This script patches VMTools ISO files by adding DS ISP executables
# and updating version information automatically with enhanced security and reliability.
# Designed to be called by external timer/scheduler systems.

# Enhanced error handling - selective strict mode
# We use selective error handling instead of full strict mode to allow proper rollback
set -u  # Exit on undefined variables
# set -e and set -o pipefail are handled selectively in critical sections

# Script metadata
readonly SCRIPT_NAME="$(basename "$0")"
readonly SCRIPT_VERSION="2.2-improved"
readonly SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Configuration
CONFIG_FILE="${CONFIG_FILE:-./fc_vmtools_patch.conf}"
readonly LOCK_FILE="/var/lock/vmtools_patch.lock"
readonly LOG_MAX_SIZE=1048576  # 1MB

# Global variables (will be set from config)
declare -g ISO_SRC=""
declare -g SRC_DIR=""
declare -g DS_ISP_DIR=""
declare -g VMTOOLS_DIR=""
declare -g ISO_SUBDIR=""
declare -g DEBUG="${DEBUG:-0}"
declare -g LOG_FILE=""
declare -g SILENT_MODE=0

# Derived paths (initialized after config load)
declare -g ISO_PATH=""
declare -g VERSION_FILE=""
declare -g TMP_DIR=""
declare -g MOUNT_DIR=""
declare -g OUTPUT_ISO=""

# Runtime flags and constants
declare -g DRY_RUN="${DRY_RUN:-0}"
declare -g FORCE_UPDATE="${FORCE_UPDATE:-0}"
declare -g ROLLBACK_MODE="${ROLLBACK_MODE:-0}"
declare -g STATUS_MODE="${STATUS_MODE:-0}"
declare -g FORCE_CONTINUE="${FORCE_CONTINUE:-0}"

# Constants
readonly REQUIRED_PARAMS=("ISO_SRC" "SRC_DIR" "DS_ISP_DIR" "VMTOOLS_DIR" "ISO_SUBDIR")
readonly SUPPORTED_ISO_TOOLS=("genisoimage" "mkisofs" "xorriso")
MAX_RETRIES="${MAX_RETRIES:-3}"
RETRY_DELAY="${RETRY_DELAY:-2}"

# Variables to track which ISO was updated
VMTOOLS_ISO_UPDATED="false"
SOURCE_ISO_UPDATED="false"

# Error handling state
SCRIPT_STATE="INITIALIZING"
BACKUP_CREATED="false"
TEMP_CREATED="false"
ISO_MOUNTED="false"

# Error handling functions
enable_strict_mode() {
    set -e
    set -o pipefail
    log_msg DEBUG "Strict mode enabled for critical section"
}

disable_strict_mode() {
    set +e
    set +o pipefail
    log_msg DEBUG "Strict mode disabled for error handling"
}

# Safe command execution with automatic rollback
safe_execute() {
    local operation_name="$1"
    local rollback_on_failure="${2:-true}"
    shift 2
    local command=("$@")

    log_msg DEBUG "Executing: $operation_name"
    SCRIPT_STATE="$operation_name"

    # Enable strict mode for the operation
    enable_strict_mode

    # Execute the command
    local exit_code=0
    if ! "${command[@]}"; then
        exit_code=$?
        disable_strict_mode

        log_msg ERROR "$operation_name failed with exit code: $exit_code"

        if [[ "$rollback_on_failure" == "true" ]] && [[ "$BACKUP_CREATED" == "true" ]]; then
            log_msg WARNING "Attempting automatic rollback due to failure in: $operation_name"
            perform_rollback
        fi

        return $exit_code
    fi

    disable_strict_mode
    log_msg DEBUG "$operation_name completed successfully"
    return 0
}

# Intelligent rollback based on script state
perform_rollback() {
    local rollback_needed=false

    log_msg INFO "Performing intelligent rollback based on script state: $SCRIPT_STATE"

    # Disable strict mode for rollback operations
    disable_strict_mode

    case "$SCRIPT_STATE" in
        "BACKUP_FILES"|"UPDATE_VERSION"|"PROCESS_UPGRADE"|"CREATE_ISO"|"REPLACE_ISO")
            if [[ "$BACKUP_CREATED" == "true" ]]; then
                log_msg INFO "Rollback needed - restoring from backup"
                rollback_needed=true
            fi
            ;;
        "MOUNT_ISO"|"COPY_ISO")
            log_msg INFO "Rollback not needed - no permanent changes made yet"
            ;;
        *)
            log_msg INFO "Rollback not applicable for current state: $SCRIPT_STATE"
            ;;
    esac

    if [[ "$rollback_needed" == "true" ]]; then
        # Call the existing rollback function
        rollback
        log_msg SUCCESS "Rollback completed successfully"
    fi
}

# Initialize paths after config is loaded
init_paths() {
    ISO_PATH="$VMTOOLS_DIR/vmtools-windows.iso"
    VERSION_FILE="$VMTOOLS_DIR/version.info"
    OUTPUT_ISO="$VMTOOLS_DIR/vmtools-windows-new.iso"

    # Use custom log file if specified in config, otherwise use default
    if [[ -z "$LOG_FILE" ]]; then
        LOG_FILE="$VMTOOLS_DIR/patch_vmtools.log"
    fi
}

# Lock file management
acquire_lock() {
    local lock_timeout=300  # 5 minutes
    local lock_acquired=0
    local start_time=$(date +%s)

    while [[ $lock_acquired -eq 0 ]]; do
        if (set -C; echo $$ > "$LOCK_FILE") 2>/dev/null; then
            lock_acquired=1
            log_msg INFO "Lock acquired: $LOCK_FILE"
        else
            local current_time=$(date +%s)
            if [[ $((current_time - start_time)) -gt $lock_timeout ]]; then
                log_msg ERROR "Failed to acquire lock within timeout"
                exit 1
            fi
            log_msg INFO "Waiting for lock... (PID: $(cat "$LOCK_FILE" 2>/dev/null || echo "unknown"))"
            sleep 5
        fi
    done
}

release_lock() {
    if [[ -f "$LOCK_FILE" ]]; then
        rm -f "$LOCK_FILE"
        log_msg DEBUG "Lock released: $LOCK_FILE"
    fi
}

# Display usage information
usage() {
    cat << EOF
VMTools Patch Script v${SCRIPT_VERSION}

USAGE:
    $SCRIPT_NAME [OPTIONS]

OPTIONS:
    -h, --help      Show this help message
    -v, --version   Show script version
    -d, --debug     Enable debug mode
    -c, --config    Specify config file (default: ./fc_vmtools_patch.conf)
    --dry-run       Show what would be done without making changes
    --rollback      Rollback to previous version
    --force         Force continue even if disk space is insufficient
    --force-update  Force update even if ISO unchanged and ds_isp exists
    --status        Show ISO status and processing history

DESCRIPTION:
    This script patches VMTools ISO files by:
    1. Checking if VMTools ISO or source ISO has been updated since last processing
    2. Adding DS ISP executables to upgrade directories
    3. Updating batch files to execute the new components
    4. Incrementing version numbers (only when VMTools ISO is updated by external modules)
    5. Creating a new ISO file

    The script tracks two types of ISO changes:
    - VMTools ISO updates (by external modules): Triggers version increment
    - Source ISO updates (DS ISP content): Does NOT increment version

    The script is designed to be called by external timer/scheduler systems.
    It will automatically detect if any ISO needs reprocessing and skip
    unnecessary work if neither ISO has changed.

EXAMPLES:
    $SCRIPT_NAME                    # Run with default config
    $SCRIPT_NAME --debug            # Run with debug output
    $SCRIPT_NAME --dry-run          # Preview changes
    $SCRIPT_NAME --rollback         # Rollback changes
    $SCRIPT_NAME --force            # Force continue despite warnings
    $SCRIPT_NAME --force-update     # Force update even if already processed
    $SCRIPT_NAME --status           # Show ISO status information

EOF
}

# Read configuration file
read_config() {
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log_msg ERROR "Configuration file not found: $CONFIG_FILE"
        cat >&2 << EOF

Please create a configuration file with the following format:
ISO_SRC=./ds-vm-isp-20.0.2.3530.iso
SRC_DIR=windows
DS_ISP_DIR=ds_isp
VMTOOLS_DIR=/opt/patch/programfiles/vmtools
ISO_SUBDIR=upgrade

Run '$SCRIPT_NAME --help' for more information.
EOF
        exit 1
    fi

    log_msg INFO "Reading configuration from: $CONFIG_FILE"

    # Source the config file
    source "$CONFIG_FILE"

    # Validate required parameters
    local missing_params=()
    for param in "${REQUIRED_PARAMS[@]}"; do
        if [[ -z "${!param:-}" ]]; then
            missing_params+=("$param")
        fi
    done

    if [[ ${#missing_params[@]} -gt 0 ]]; then
        log_msg ERROR "Missing required parameters in configuration file: $CONFIG_FILE"
        log_msg ERROR "Missing parameters: ${missing_params[*]}"
        exit 1
    fi

    # Convert relative paths to absolute paths
    if [[ "$ISO_SRC" != /* ]]; then
        if command -v realpath >/dev/null 2>&1; then
            ISO_SRC="$(realpath "$ISO_SRC" 2>/dev/null || echo "$PWD/$ISO_SRC")"
        else
            ISO_SRC="$PWD/$ISO_SRC"
        fi
    fi
    if [[ "$VMTOOLS_DIR" != /* ]]; then
        if command -v realpath >/dev/null 2>&1; then
            VMTOOLS_DIR="$(realpath "$VMTOOLS_DIR" 2>/dev/null || echo "$PWD/$VMTOOLS_DIR")"
        else
            VMTOOLS_DIR="$PWD/$VMTOOLS_DIR"
        fi
    fi
}

# Print messages
print_info() {
    echo "[INFO] $1"
}

print_success() {
    echo "[SUCCESS] $1"
}

print_warning() {
    echo "[WARNING] $1"
}

print_error() {
    echo "[ERROR] $1" >&2
}

# Enhanced logging with structured format
log_msg() {
    local level="$1"
    local msg="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    local pid=$$

    # Ensure log directory exists
    if [[ -n "${LOG_FILE:-}" ]]; then
        mkdir -p "$(dirname "$LOG_FILE")" 2>/dev/null || true
        rotate_log_if_needed
    fi

    # Format: [TIMESTAMP] [PID] [LEVEL] MESSAGE
    local log_entry="[$timestamp] [$pid] [$level] $msg"

    case "$level" in
        ERROR)
            [[ "$SILENT_MODE" != "1" ]] && print_error "$msg"
            [[ -n "${LOG_FILE:-}" ]] && echo "$log_entry" >> "$LOG_FILE" 2>/dev/null || true
            ;;
        INFO)
            if [[ "$SILENT_MODE" != "1" ]]; then
                if [[ "${DRY_RUN:-0}" == "1" ]]; then
                    print_info "[DRY-RUN] $msg"
                else
                    print_info "$msg"
                fi
            fi
            [[ -n "${LOG_FILE:-}" ]] && echo "$log_entry" >> "$LOG_FILE" 2>/dev/null || true
            ;;
        SUCCESS)
            [[ "$SILENT_MODE" != "1" ]] && print_success "$msg"
            [[ -n "${LOG_FILE:-}" ]] && echo "$log_entry" >> "$LOG_FILE" 2>/dev/null || true
            ;;
        WARNING)
            [[ "$SILENT_MODE" != "1" ]] && print_warning "$msg"
            [[ -n "${LOG_FILE:-}" ]] && echo "$log_entry" >> "$LOG_FILE" 2>/dev/null || true
            ;;
        DEBUG)
            if [[ "${DEBUG:-0}" == "1" ]]; then
                [[ "$SILENT_MODE" != "1" ]] && echo "[DEBUG] $msg"
                [[ -n "${LOG_FILE:-}" ]] && echo "$log_entry" >> "$LOG_FILE" 2>/dev/null || true
            fi
            ;;
    esac
}

# Log rotation function
rotate_log_if_needed() {
    if [[ -n "${LOG_FILE:-}" ]] && [[ -f "$LOG_FILE" ]] && [[ $(stat -c%s "$LOG_FILE" 2>/dev/null || echo 0) -ge ${LOG_MAX_SIZE:-1048576} ]]; then
        mv "$LOG_FILE" "$LOG_FILE.bak" 2>/dev/null || true
        # Don't call log_msg here to avoid recursion
        echo "[INFO] Log rotated to $LOG_FILE.bak"
    fi
}

# Backup file if not already exists
backup_file() {
    local src="$1"
    local bak="$2"

    if [[ -f "$bak" ]]; then
        log_msg DEBUG "$bak already exists, skip backup."
    else
        cp "$src" "$bak"
        log_msg INFO "Backup created: $src -> $bak"
    fi
}

# Rollback ISO and version.info from backup
rollback() {
    local iso_bak="$VMTOOLS_DIR/vmtools-windows.iso.bak"
    local ver_bak="$VMTOOLS_DIR/version.info.bak"
    local rollback_success=0

    log_msg INFO "Starting rollback process..."

    if [[ -f "$iso_bak" ]]; then
        if mv "$iso_bak" "$ISO_PATH"; then
            log_msg SUCCESS "ISO rolled back successfully"
            rollback_success=1
        else
            log_msg ERROR "Failed to rollback ISO file"
        fi
    else
        log_msg WARNING "ISO backup not found, cannot rollback ISO"
    fi

    if [[ -f "$ver_bak" ]]; then
        if mv "$ver_bak" "$VERSION_FILE"; then
            log_msg SUCCESS "version.info rolled back successfully"
            rollback_success=1
        else
            log_msg ERROR "Failed to rollback version.info"
        fi
    else
        log_msg WARNING "version.info backup not found, cannot rollback version file"
    fi

    if [[ $rollback_success -eq 0 ]]; then
        log_msg ERROR "No backup files found for rollback"
        exit 1
    fi
}



# Secure temporary directory creation
create_secure_temp_dir() {
    if ! TMP_DIR=$(mktemp -d -t vmtools_patch.XXXXXX); then
        log_msg ERROR "Failed to create secure temporary directory"
        exit 1
    fi

    if ! MOUNT_DIR=$(mktemp -d -t vmtools_mount.XXXXXX); then
        log_msg ERROR "Failed to create secure mount directory"
        rm -rf "$TMP_DIR"
        exit 1
    fi

    # Set restrictive permissions
    chmod 700 "$TMP_DIR" "$MOUNT_DIR"
    log_msg DEBUG "Created secure temporary directories: $TMP_DIR, $MOUNT_DIR"
}

# Calculate checksum for a given file
calculate_checksum() {
    local file_path="$1"
    local checksum

    if command -v sha256sum >/dev/null 2>&1; then
        checksum=$(sha256sum "$file_path" | cut -d' ' -f1)
    elif command -v shasum >/dev/null 2>&1; then
        checksum=$(shasum -a 256 "$file_path" | cut -d' ' -f1)
    else
        # Fallback to file size and modification time
        checksum="$(stat -c '%s-%Y' "$file_path" 2>/dev/null || echo "unknown")"
    fi

    echo "$checksum"
}

# Generic function to detect if an ISO file has been updated
detect_iso_file_update() {
    local iso_file="$1"
    local checksum_file="$2"
    local iso_type="$3"  # "VMTools" or "Source"

    local current_checksum
    current_checksum=$(calculate_checksum "$iso_file")

    log_msg DEBUG "Current $iso_type ISO checksum: $current_checksum"

    # Check if checksum file exists and compare
    if [[ -f "$checksum_file" ]]; then
        local stored_info=$(cat "$checksum_file" 2>/dev/null || echo "")
        local stored_checksum=$(echo "$stored_info" | cut -d'|' -f1)

        if [[ "$current_checksum" != "$stored_checksum" ]]; then
            if [[ "$iso_type" == "VMTools" ]]; then
                log_msg INFO "$iso_type ISO file has been updated by external modules (checksum changed)"
            else
                log_msg INFO "$iso_type ISO file has been updated (checksum changed)"
            fi
            log_msg DEBUG "Previous $iso_type ISO checksum: $stored_checksum"
            log_msg DEBUG "Current $iso_type ISO checksum: $current_checksum"
            return 0  # ISO has been updated
        else
            log_msg DEBUG "$iso_type ISO file unchanged (checksum match)"
            return 1  # ISO has not been updated
        fi
    else
        log_msg INFO "No previous $iso_type ISO checksum found, treating as new ISO"
        return 0  # No previous checksum, treat as updated
    fi
}

# Detect if VMTools ISO has been updated by external modules
detect_vmtools_iso_update() {
    detect_iso_file_update "$ISO_PATH" "$VMTOOLS_DIR/.vmtools_iso_checksum" "VMTools"
}

# Detect if source ISO has been updated
detect_source_iso_update() {
    detect_iso_file_update "$ISO_SRC" "$VMTOOLS_DIR/.source_iso_checksum" "Source"
}



# Generic function to store ISO checksum
store_iso_file_checksum() {
    local iso_file="$1"
    local checksum_file="$2"
    local iso_type="$3"  # "VMTools" or "Source"

    local current_checksum
    current_checksum=$(calculate_checksum "$iso_file")

    # Store checksum with timestamp
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    if echo "$current_checksum|$timestamp" > "$checksum_file"; then
        log_msg DEBUG "Stored $iso_type ISO checksum: $current_checksum at $timestamp"
    else
        log_msg WARNING "Failed to store $iso_type ISO checksum"
    fi
}

# Store current VMTools ISO checksum for future comparison
store_vmtools_iso_checksum() {
    store_iso_file_checksum "$ISO_PATH" "$VMTOOLS_DIR/.vmtools_iso_checksum" "VMTools"
}

# Store current source ISO checksum for future comparison
store_source_iso_checksum() {
    store_iso_file_checksum "$ISO_SRC" "$VMTOOLS_DIR/.source_iso_checksum" "Source"
}

# Store both ISO checksums for future comparison
store_iso_checksum() {
    store_vmtools_iso_checksum
    store_source_iso_checksum
}

# Enhanced cleanup with intelligent error handling
cleanup_resources() {
    local exit_code=$?
    local cleanup_exit_code=0

    # Disable strict mode for cleanup operations
    disable_strict_mode

    log_msg DEBUG "Starting cleanup process (exit_code: $exit_code, state: $SCRIPT_STATE)..."

    # If we're exiting with an error and have made changes, consider rollback
    if [[ $exit_code -ne 0 ]] && [[ "$SCRIPT_STATE" != "INITIALIZING" ]] && [[ "$SCRIPT_STATE" != "COMPLETED" ]]; then
        log_msg WARNING "Script failed in state: $SCRIPT_STATE"

        # Only perform rollback if we haven't already done it and backups exist
        if [[ "$BACKUP_CREATED" == "true" ]] && [[ ! -f "$VMTOOLS_DIR/.rollback_performed" ]]; then
            log_msg INFO "Performing emergency rollback due to script failure"
            perform_rollback
            # Mark rollback as performed to avoid double rollback
            touch "$VMTOOLS_DIR/.rollback_performed" 2>/dev/null || true
        fi
    fi

    # Unmount any mounted filesystems
    if [[ -n "${MOUNT_DIR:-}" ]] && mountpoint -q "$MOUNT_DIR" 2>/dev/null; then
        log_msg DEBUG "Unmounting: $MOUNT_DIR"
        if ! sudo umount "$MOUNT_DIR" 2>/dev/null; then
            log_msg WARNING "Failed to unmount $MOUNT_DIR, forcing..."
            if ! sudo umount -f "$MOUNT_DIR" 2>/dev/null; then
                log_msg ERROR "Failed to force unmount $MOUNT_DIR"
                cleanup_exit_code=1
            fi
        fi
        ISO_MOUNTED="false"
    fi

    # Remove temporary directories
    if [[ -n "${TMP_DIR:-}" ]] && [[ -d "$TMP_DIR" ]]; then
        if rm -rf "$TMP_DIR" 2>/dev/null; then
            log_msg DEBUG "Removed temporary directory: $TMP_DIR"
            TEMP_CREATED="false"
        else
            log_msg WARNING "Failed to remove temporary directory: $TMP_DIR"
            cleanup_exit_code=1
        fi
    fi

    if [[ -n "${MOUNT_DIR:-}" ]] && [[ -d "$MOUNT_DIR" ]]; then
        if rmdir "$MOUNT_DIR" 2>/dev/null; then
            log_msg DEBUG "Removed mount directory: $MOUNT_DIR"
        else
            log_msg WARNING "Failed to remove mount directory: $MOUNT_DIR"
        fi
    fi

    # Release lock
    release_lock

    # Clean up rollback marker
    rm -f "$VMTOOLS_DIR/.rollback_performed" 2>/dev/null || true

    # Report final status
    if [[ $exit_code -eq 0 ]] && [[ $cleanup_exit_code -eq 0 ]]; then
        log_msg DEBUG "Cleanup completed successfully"
    elif [[ $exit_code -ne 0 ]]; then
        log_msg ERROR "Script failed with exit code: $exit_code"
    else
        log_msg WARNING "Cleanup completed with warnings"
    fi

    # Exit with the original exit code, not cleanup exit code
    exit $exit_code
}

# Enhanced error handling with retry mechanism (compatible with original retry_operation)
retry_with_backoff() {
    local operation_name="$1"
    local max_attempts="${2:-3}"
    local base_delay="${3:-2}"
    shift 3
    local command=("$@")

    local attempt=1
    local delay=$base_delay

    while [[ $attempt -le $max_attempts ]]; do
        log_msg DEBUG "Attempting $operation_name (attempt $attempt/$max_attempts)"

        if "${command[@]}" 2>/dev/null; then
            if [[ $attempt -gt 1 ]]; then
                log_msg INFO "$operation_name succeeded on attempt $attempt"
            fi
            return 0
        fi

        local exit_code=$?

        # Check for non-retryable errors
        case "$operation_name" in
            *mount*)
                # Check if it's a permission or file not found error
                if [[ $exit_code -eq 1 ]] || [[ $exit_code -eq 2 ]]; then
                    log_msg DEBUG "$operation_name failed with non-retryable error (exit code: $exit_code)"
                    return $exit_code
                fi
                ;;
            *copy*)
                # Check if it's a permission or space error
                if [[ $exit_code -eq 1 ]] && [[ $attempt -eq 1 ]]; then
                    # First attempt failed, check if it's likely a permanent error
                    log_msg DEBUG "Checking if copy error is retryable..."
                fi
                ;;
        esac

        if [[ $attempt -eq $max_attempts ]]; then
            log_msg ERROR "$operation_name failed after $max_attempts attempts (exit code: $exit_code)"
            return $exit_code
        fi

        log_msg WARNING "$operation_name failed on attempt $attempt (exit code: $exit_code), retrying in ${delay}s..."
        sleep "$delay"

        # Exponential backoff for mount operations
        if [[ "$operation_name" == *mount* ]] && [[ $attempt -gt 1 ]]; then
            delay=$((delay * 2))
        fi
        ((attempt++))
    done
}

# Alias for compatibility with original script
retry_operation() {
    retry_with_backoff "$@"
}

# Increment version number with better error handling
inc_version() {
    local ver="$1"
    local prefix="${ver%.*}."
    local last="${ver##*.}"

    # Ensure last is numeric
    if ! [[ "$last" =~ ^[0-9]+$ ]]; then
        log_msg ERROR "Invalid version format: $ver"
        return 1
    fi

    last=$((10#$last + 1))
    printf "%s%03d" "$prefix" "$last"
}

# Check if all subdirectories already have ds_isp directory
all_ds_isp_exist() {
    local upgrade_path="$TMP_DIR/$ISO_SUBDIR"

    if [[ ! -d "$upgrade_path" ]]; then
        log_msg ERROR "Upgrade directory not found: $upgrade_path"
        return 1
    fi

    while IFS= read -r -d '' win_dir; do
        if [[ ! -d "$win_dir/$DS_ISP_DIR" ]]; then
            return 1  # Found a directory without ds_isp, continue processing
        fi
    done < <(find "$upgrade_path" -mindepth 1 -maxdepth 1 -type d -print0)

    return 0  # All directories have ds_isp, exit
}

# Clean existing ds_isp directories when ISO is updated
clean_existing_ds_isp_dirs() {
    local upgrade_path="$TMP_DIR/$ISO_SUBDIR"
    local cleaned_count=0

    log_msg INFO "Cleaning existing $DS_ISP_DIR directories due to ISO update..."

    while IFS= read -r -d '' win_dir; do
        local ds_isp_path="$win_dir/$DS_ISP_DIR"
        if [[ -d "$ds_isp_path" ]]; then
            log_msg DEBUG "Removing existing $DS_ISP_DIR from: $(basename "$win_dir")"
            if rm -rf "$ds_isp_path"; then
                ((cleaned_count++))
            else
                log_msg WARNING "Failed to remove: $ds_isp_path"
            fi
        fi

        # Also clean up batch file modifications
        local bat_file="$win_dir/upg.bat"
        local bat_backup="$win_dir/upg.bat.bak"
        if [[ -f "$bat_backup" ]]; then
            log_msg DEBUG "Restoring original batch file: $(basename "$win_dir")/upg.bat"
            if mv "$bat_backup" "$bat_file"; then
                log_msg DEBUG "Restored original batch file"
            else
                log_msg WARNING "Failed to restore batch file: $bat_file"
            fi
        fi
    done < <(find "$upgrade_path" -mindepth 1 -maxdepth 1 -type d -print0)

    if [[ $cleaned_count -gt 0 ]]; then
        log_msg SUCCESS "Cleaned $cleaned_count existing $DS_ISP_DIR directories"
    fi
}

# Check and exit if all ds_isp already exist and ISO hasn't been updated
check_and_exit_if_all_exist() {
    local vmtools_iso_updated=false
    local source_iso_updated=false
    local force_update=false

    # Check if force update is requested
    if [[ "$FORCE_UPDATE" == "1" ]]; then
        force_update=true
        log_msg INFO "Force update requested, will reprocess all directories"
    fi

    # Check if VMTools ISO has been updated
    if detect_vmtools_iso_update; then
        vmtools_iso_updated=true
        log_msg INFO "VMTools ISO has been updated by external modules, will reprocess all directories"
    fi

    # Check if source ISO has been updated
    if detect_source_iso_update; then
        source_iso_updated=true
        log_msg INFO "Source ISO has been updated, will reprocess all directories"
    fi

    # If no force update, no ISO updates, and all directories already have ds_isp, exit
    if [[ "$force_update" == "false" ]] && [[ "$vmtools_iso_updated" == "false" ]] && [[ "$source_iso_updated" == "false" ]] && all_ds_isp_exist; then
        log_msg INFO "All subdirectories already have $DS_ISP_DIR and no ISO changes detected, nothing to do. Exiting."
        log_msg INFO "Use --force-update to force reprocessing."
        cleanup_resources
        exit 0
    fi

    # Set global variables to track what was updated (for version update logic)
    VMTOOLS_ISO_UPDATED="$vmtools_iso_updated"
    SOURCE_ISO_UPDATED="$source_iso_updated"

    # If any ISO was updated or force update requested, clean existing ds_isp directories
    if [[ "$vmtools_iso_updated" == "true" ]] || [[ "$source_iso_updated" == "true" ]] || [[ "$force_update" == "true" ]]; then
        clean_existing_ds_isp_dirs
    fi
}

# Insert ds_isp directory and files from source ISO, update upg.bat
process_upgrade_dirs() {
    local upgrade_path="$TMP_DIR/$ISO_SUBDIR"
    local src_mount_dir

    # Create secure mount directory for source ISO
    if ! src_mount_dir=$(mktemp -d -t ds_isp_iso.XXXXXX); then
        log_msg ERROR "Failed to create secure mount directory for source ISO"
        return 1
    fi
    chmod 700 "$src_mount_dir"

    if [[ "${DRY_RUN:-0}" == "1" ]]; then
        log_msg INFO "Would process upgrade directories with files from ISO: $ISO_SRC"
        rmdir "$src_mount_dir"
        return 0
    fi

    log_msg INFO "Processing upgrade directories..."

    # Mount source ISO
    log_msg INFO "Mounting source ISO: $ISO_SRC"
    enable_strict_mode
    if ! retry_operation "mount source ISO" "$MAX_RETRIES" "$RETRY_DELAY" sudo mount -o loop,ro "$ISO_SRC" "$src_mount_dir"; then
        disable_strict_mode
        log_msg ERROR "Failed to mount source ISO: $ISO_SRC"
        rmdir "$src_mount_dir" 2>/dev/null
        return 1
    fi
    disable_strict_mode

    # Check if source directory exists in ISO
    local src_path="$src_mount_dir/$SRC_DIR"
    if [[ ! -d "$src_path" ]]; then
        log_msg ERROR "Source directory '$SRC_DIR' not found in ISO: $ISO_SRC"
        sudo umount "$src_mount_dir"
        rmdir "$src_mount_dir"
        return 1
    fi

    # Count directories for progress
    local total_dirs
    total_dirs=$(find "$upgrade_path" -mindepth 1 -maxdepth 1 -type d | wc -l)
    local current_dir=0
    local failed_dirs=0

    while IFS= read -r -d '' win_dir; do
        ((current_dir++))
        local dir_name
        dir_name=$(basename "$win_dir")

        # Processing directory
        log_msg DEBUG "Processing directory $current_dir/$total_dirs: $dir_name"

        local ds_isp_path="$win_dir/$DS_ISP_DIR"

        # Skip if directory already exists and contains files
        if [[ -d "$ds_isp_path" ]] && [[ -n "$(ls -A "$ds_isp_path" 2>/dev/null)" ]]; then
            log_msg DEBUG "Directory $dir_name already has $DS_ISP_DIR, skipping"
            continue
        fi

        # Create directory
        if ! mkdir -p "$ds_isp_path"; then
            log_msg ERROR "Failed to create directory: $ds_isp_path"
            ((failed_dirs++))
            continue
        fi

        # Copy all files from source directory
        enable_strict_mode
        if ! retry_operation "copy files to $ds_isp_path" "$MAX_RETRIES" "$RETRY_DELAY" cp -r "$src_path"/* "$ds_isp_path/"; then
            disable_strict_mode
            log_msg ERROR "Failed to copy files to: $ds_isp_path/"
            ((failed_dirs++))
            continue
        fi
        disable_strict_mode

        # Update batch file
        local bat_file="$win_dir/upg.bat"
        if [[ -f "$bat_file" ]]; then
            # Create backup of original batch file
            if ! cp "$bat_file" "$bat_file.bak"; then
                log_msg WARNING "Failed to backup batch file: $bat_file"
            fi

            local orig_content
            if ! orig_content=$(cat "$bat_file"); then
                log_msg ERROR "Failed to read batch file: $bat_file"
                ((failed_dirs++))
                continue
            fi

            # Check if our content is already present
            if grep -q "$DS_ISP_DIR" "$bat_file"; then
                log_msg DEBUG "Batch file $dir_name/upg.bat already contains $DS_ISP_DIR"
                continue
            fi

            local insert_content="echo \"%DATE% %TIME:~0,-3% : [upg.bat] running '$DS_ISP_DIR' ...\" >> %logfile%
call \"%upgrade_patch%\\$DS_ISP_DIR\\util_ds_filefilter_manage.bat\" install
if !errorlevel! NEQ 0 (
    echo \"%DATE% %TIME:~0,-3% : [upg.bat] '$DS_ISP_DIR' failed, code=!errorlevel!\" >> %logfile%
    exit 1
) else (
    echo \"%DATE% %TIME:~0,-3% : [upg.bat] '$DS_ISP_DIR' completed successfully\" >> %logfile%
    exit 0
)
"

            if {
                echo -e "$insert_content"
                echo "$orig_content"
            } > "$bat_file"; then
                log_msg DEBUG "Updated batch file: $dir_name/upg.bat"
            else
                log_msg ERROR "Failed to update batch file: $bat_file"
                # Restore backup
                if [[ -f "$bat_file.bak" ]]; then
                    mv "$bat_file.bak" "$bat_file"
                fi
                ((failed_dirs++))
            fi
        else
            log_msg WARNING "Batch file not found: $bat_file"
        fi
    done < <(find "$upgrade_path" -mindepth 1 -maxdepth 1 -type d -print0)

    # Cleanup source ISO mount
    if ! retry_operation "unmount source ISO" "$MAX_RETRIES" "$RETRY_DELAY" sudo umount "$src_mount_dir"; then
        log_msg WARNING "Failed to unmount $src_mount_dir"
    fi

    if ! rmdir "$src_mount_dir" 2>/dev/null; then
        log_msg WARNING "Failed to remove mount directory $src_mount_dir"
    fi

    # Report results
    local processed_dirs=$((total_dirs - failed_dirs))
    if [[ $failed_dirs -eq 0 ]]; then
        log_msg SUCCESS "Successfully processed all $total_dirs upgrade directories"
    elif [[ $processed_dirs -gt 0 ]]; then
        log_msg WARNING "Processed $processed_dirs/$total_dirs directories ($failed_dirs failed)"
        log_msg WARNING "Some directories may not have been updated properly"
    else
        log_msg ERROR "Failed to process any directories"
        return 1
    fi

    return 0
}



# Check environment and required files
check_env() {
    log_msg INFO "Checking environment and dependencies..."

    # Check required files
    log_msg DEBUG "Checking required files..."
    log_msg DEBUG "  Checking ISO_SRC: $ISO_SRC"
    log_msg DEBUG "  Checking ISO_PATH: $ISO_PATH"
    log_msg DEBUG "  Checking VERSION_FILE: $VERSION_FILE"

    local missing_files=()
    [[ ! -f "$ISO_SRC" ]] && missing_files+=("Source ISO: $ISO_SRC")
    [[ ! -f "$ISO_PATH" ]] && missing_files+=("VMTools ISO file: $ISO_PATH")
    [[ ! -f "$VERSION_FILE" ]] && missing_files+=("Version file: $VERSION_FILE")

    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_msg ERROR "Missing required files:"
        for file in "${missing_files[@]}"; do
            log_msg ERROR "  - $file"
        done
        exit 1
    fi

    # Check for ISO creation tools
    local iso_tool=""
    for tool in "${SUPPORTED_ISO_TOOLS[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            iso_tool="$tool"
            break
        fi
    done

    if [[ -z "$iso_tool" ]]; then
        log_msg ERROR "No supported ISO creation tool found."
        log_msg ERROR "Please install one of: ${SUPPORTED_ISO_TOOLS[*]}"
        exit 1
    fi

    log_msg DEBUG "Using ISO tool: $iso_tool"

    # Check disk space
    if ! check_disk_space; then
        log_msg ERROR "Insufficient disk space to proceed"
        log_msg ERROR "Please free up disk space or use a different location"
        exit 1
    fi

    # Check permissions
    check_permissions

    log_msg SUCCESS "Environment check passed"
}

# Check available disk space
check_disk_space() {
    local iso_size
    iso_size=$(stat -c%s "$ISO_PATH" 2>/dev/null || echo 0)

    # Calculate actual space requirements:
    # 1. Backup file: 1x ISO size (created at start, kept until end)
    # 2. Temporary directory: ~1x ISO size (during processing)
    # 3. New ISO file: 1x ISO size (created at end, before cleanup)
    # Peak usage occurs when all three exist simultaneously = 3x ISO size
    # Adding 10% safety margin for filesystem overhead
    local required_space=$((iso_size * 33 / 10))  # 3.3x ISO size with safety margin

    # Add source ISO size if it's different from VMTools ISO
    local src_iso_size=0
    if [[ -f "$ISO_SRC" ]] && [[ "$ISO_SRC" != "$ISO_PATH" ]]; then
        src_iso_size=$(stat -c%s "$ISO_SRC" 2>/dev/null || echo 0)
        # Add space for DS ISP files copied to each directory
        # Estimate: source ISO size / 10 (assuming DS ISP is small portion)
        required_space=$((required_space + src_iso_size / 10))
    fi

    local available_space
    available_space=$(df "$VMTOOLS_DIR" | awk 'NR==2 {print $4 * 1024}')

    log_msg DEBUG "Disk space analysis:"
    log_msg DEBUG "  VMTools ISO size: $(numfmt --to=iec $iso_size)"
    log_msg DEBUG "  Source ISO size: $(numfmt --to=iec $src_iso_size)"
    log_msg DEBUG "  Estimated peak usage: $(numfmt --to=iec $required_space)"
    log_msg DEBUG "  Available space: $(numfmt --to=iec $available_space)"

    if [[ $available_space -lt $required_space ]]; then
        log_msg ERROR "Insufficient disk space detected"
        log_msg ERROR "Required: $(numfmt --to=iec $required_space), Available: $(numfmt --to=iec $available_space)"
        log_msg ERROR "Shortage: $(numfmt --to=iec $((required_space - available_space)))"

        # Allow force continue if requested
        if [[ "$FORCE_CONTINUE" == "1" ]]; then
            log_msg WARNING "FORCED CONTINUE: Proceeding despite insufficient disk space"
            log_msg WARNING "This may cause the script to fail during execution"
            return 0
        fi

        return 1
    else
        log_msg SUCCESS "Disk space check passed ($(numfmt --to=iec $available_space) available)"
        return 0
    fi
}

# Check file permissions
check_permissions() {
    local perm_errors=()

    [[ ! -r "$ISO_SRC" ]] && perm_errors+=("Cannot read: $ISO_SRC")
    [[ ! -r "$ISO_PATH" ]] && perm_errors+=("Cannot read: $ISO_PATH")
    [[ ! -w "$VMTOOLS_DIR" ]] && perm_errors+=("Cannot write to: $VMTOOLS_DIR")

    if [[ ${#perm_errors[@]} -gt 0 ]]; then
        log_msg ERROR "Permission errors detected:"
        for error in "${perm_errors[@]}"; do
            log_msg ERROR "  - $error"
        done
        exit 1
    fi
}

# Mount ISO and copy content to temp dir with state tracking
mount_and_copy_iso() {
    SCRIPT_STATE="MOUNT_ISO"

    if [[ "${DRY_RUN:-0}" == "1" ]]; then
        log_msg INFO "Would mount and copy ISO content"
        # Still need to mount for dry run analysis
        if ! retry_operation "mount ISO for dry run" "$MAX_RETRIES" "$RETRY_DELAY" sudo mount -o loop,ro "$ISO_PATH" "$MOUNT_DIR"; then
            log_msg ERROR "Failed to mount ISO for dry run analysis"
            return 1
        fi
        cp -r "$MOUNT_DIR"/* "$TMP_DIR"
        sudo umount "$MOUNT_DIR"
        return 0
    fi

    log_msg INFO "Mounting and copying ISO content..."

    # Mount ISO with error handling
    if ! retry_operation "mount VMTools ISO" "$MAX_RETRIES" "$RETRY_DELAY" sudo mount -o loop,ro "$ISO_PATH" "$MOUNT_DIR"; then
        log_msg ERROR "Failed to mount ISO: $ISO_PATH"
        return 1
    fi

    ISO_MOUNTED="true"
    log_msg DEBUG "ISO mounted at: $MOUNT_DIR"

    # Copy content with progress indication
    SCRIPT_STATE="COPY_ISO"
    log_msg INFO "Copying ISO content..."
    if ! retry_operation "copy ISO content" "$MAX_RETRIES" "$RETRY_DELAY" cp -r "$MOUNT_DIR"/* "$TMP_DIR"; then
        log_msg ERROR "Failed to copy ISO content"
        sudo umount "$MOUNT_DIR"
        ISO_MOUNTED="false"
        return 1
    fi

    log_msg DEBUG "ISO content copied to: $TMP_DIR"

    # Cleanup mount
    if ! retry_operation "unmount VMTools ISO" "$MAX_RETRIES" "$RETRY_DELAY" sudo umount "$MOUNT_DIR"; then
        log_msg WARNING "Failed to unmount $MOUNT_DIR"
    else
        ISO_MOUNTED="false"
    fi

    # Verify ISO subdir exists
    if [[ ! -d "$TMP_DIR/$ISO_SUBDIR" ]]; then
        log_msg ERROR "ISO subdirectory not found: $ISO_SUBDIR"
        log_msg ERROR "Available directories in ISO:"
        ls -la "$TMP_DIR" | grep "^d" | awk '{print "  - " $9}' | grep -v "^\.$\|^\.\.$"
        return 1
    fi

    log_msg SUCCESS "ISO content prepared successfully"
    return 0
}

# Backup ISO and version.info with state tracking
backup_files() {
    log_msg INFO "Creating backups..."

    if safe_execute "BACKUP_FILES" true backup_file "$VERSION_FILE" "$VMTOOLS_DIR/version.info.bak" && \
       safe_execute "BACKUP_FILES" true backup_file "$ISO_PATH" "$VMTOOLS_DIR/vmtools-windows.iso.bak"; then
        BACKUP_CREATED="true"
        log_msg SUCCESS "Backups created successfully"
    else
        log_msg ERROR "Failed to create backups"
        return 1
    fi
}

# Update version.info and copy to temp dir with state tracking
update_version_info() {
    SCRIPT_STATE="UPDATE_VERSION"
    local linuxver winver

    # Disable strict mode for reading version info
    disable_strict_mode

    linuxver=$(grep '^linuxversion=' "$VERSION_FILE" | cut -d= -f2)
    winver=$(grep '^windowsversion=' "$VERSION_FILE" | cut -d= -f2)

    if [[ -z "$linuxver" ]] || [[ -z "$winver" ]]; then
        log_msg ERROR "Failed to read version information from $VERSION_FILE"
        return 1
    fi

    # Only update version if VMTools ISO was updated by external modules
    # Do NOT update version if only source ISO was updated
    if [[ "$VMTOOLS_ISO_UPDATED" == "true" ]]; then
        log_msg INFO "Updating version information due to VMTools ISO update..."

        local new_winver
        new_winver=$(inc_version "$winver")

        if [[ -z "$new_winver" ]]; then
            log_msg ERROR "Failed to increment version: $winver"
            return 1
        fi

        # Enable strict mode for version file update
        enable_strict_mode

        # Update only Windows version, keep Linux version unchanged
        if ! sed -i "s/^windowsversion=.*/windowsversion=$new_winver/" "$VERSION_FILE"; then
            disable_strict_mode
            log_msg ERROR "Failed to update version file: $VERSION_FILE"
            return 1
        fi

        disable_strict_mode
        log_msg INFO "Version updated: linux=$linuxver (unchanged), windows=$new_winver"
    else
        log_msg INFO "Keeping version information unchanged (only source ISO updated or force update)"
        log_msg INFO "Current versions: linux=$linuxver, windows=$winver"
    fi

    # Always copy version.info to temp directory (whether updated or not)
    enable_strict_mode
    if ! cp "$VERSION_FILE" "$TMP_DIR/version.info"; then
        disable_strict_mode
        log_msg ERROR "Failed to copy version file to temp directory"
        return 1
    fi

    disable_strict_mode
    return 0
}

# Make new ISO and cleanup with state tracking
make_new_iso_and_cleanup() {
    SCRIPT_STATE="CREATE_ISO"

    if [[ "${DRY_RUN:-0}" == "1" ]]; then
        log_msg INFO "Would create new ISO: $OUTPUT_ISO"
        return 0
    fi

    log_msg INFO "Creating new ISO..."

    local retval=0
    local iso_tool=""

    # Find available ISO creation tool
    for tool in "${SUPPORTED_ISO_TOOLS[@]}"; do
        if command -v "$tool" >/dev/null 2>&1; then
            iso_tool="$tool"
            break
        fi
    done

    if [[ -z "$iso_tool" ]]; then
        log_msg ERROR "No ISO creation tool found"
        return 1
    fi

    # Enable strict mode for ISO creation
    enable_strict_mode

    # Create ISO with appropriate tool
    case "$iso_tool" in
        genisoimage)
            genisoimage -o "$OUTPUT_ISO" -J -R -V "VMTOOLS" -quiet "$TMP_DIR"
            retval=$?
            ;;
        mkisofs)
            mkisofs -o "$OUTPUT_ISO" -J -R -V "VMTOOLS" -quiet "$TMP_DIR"
            retval=$?
            ;;
        xorriso)
            xorriso -as mkisofs -o "$OUTPUT_ISO" -J -R -V "VMTOOLS" "$TMP_DIR" 2>/dev/null
            retval=$?
            ;;
    esac

    disable_strict_mode

    if [[ $retval -ne 0 ]]; then
        log_msg ERROR "ISO creation failed with $iso_tool (exit code: $retval)"
        return 1
    fi

    # Verify new ISO
    if [[ ! -f "$OUTPUT_ISO" ]]; then
        log_msg ERROR "ISO file was not created: $OUTPUT_ISO"
        return 1
    fi

    local new_size
    new_size=$(stat -c%s "$OUTPUT_ISO" 2>/dev/null || echo 0)
    if [[ $new_size -eq 0 ]]; then
        log_msg ERROR "Created ISO file is empty: $OUTPUT_ISO"
        return 1
    fi

    log_msg SUCCESS "ISO created successfully: $OUTPUT_ISO ($(numfmt --to=iec $new_size))"

    # Replace original ISO with new one
    SCRIPT_STATE="REPLACE_ISO"
    enable_strict_mode

    if ! mv "$OUTPUT_ISO" "$ISO_PATH"; then
        disable_strict_mode
        log_msg ERROR "Failed to replace original ISO"
        return 1
    fi

    disable_strict_mode
    log_msg SUCCESS "Original ISO updated successfully"

    # Store new ISO checksum for future comparison
    store_iso_checksum

    return 0
}

# Create summary report
create_summary() {
    # Write summary directly to log file
    log_msg INFO "==================== OPERATION SUMMARY ===================="
    log_msg INFO "Date: $(date)"
    log_msg INFO "Script Version: $SCRIPT_VERSION"
    log_msg INFO "Config File: $CONFIG_FILE"
    log_msg INFO ""
    log_msg INFO "Configuration:"
    log_msg INFO "- Source ISO: $ISO_SRC"
    log_msg INFO "- Source Directory: $SRC_DIR"
    log_msg INFO "- DS ISP Directory: $DS_ISP_DIR"
    log_msg INFO "- VMTools Directory: $VMTOOLS_DIR"
    log_msg INFO "- ISO Subdirectory: $ISO_SUBDIR"
    log_msg INFO ""
    log_msg INFO "Files Processed:"
    log_msg INFO "- Original ISO: $ISO_PATH"
    log_msg INFO "- Version File: $VERSION_FILE"
    log_msg INFO "- Log File: $LOG_FILE"
    log_msg INFO ""
    log_msg INFO "Results:"
    log_msg INFO "- Backup Created: $([ -f "$VMTOOLS_DIR/vmtools-windows.iso.bak" ] && echo "Yes" || echo "No")"
    log_msg INFO "- Version Updated: $([ -f "$VERSION_FILE" ] && echo "Yes" || echo "No")"
    log_msg INFO "- ISO Updated: $([ -f "$ISO_PATH" ] && echo "Yes" || echo "No")"

    if [[ -f "$VERSION_FILE" ]]; then
        log_msg INFO ""
        log_msg INFO "Version Information:"
        while IFS= read -r line; do
            log_msg INFO "- $line"
        done < <(grep -E "^(linux|windows)version=" "$VERSION_FILE")
    fi

    log_msg INFO "============================================================="
}

# Parse command line arguments
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                usage
                exit 0
                ;;
            -v|--version)
                echo "VMTools Patch Script v${SCRIPT_VERSION}"
                exit 0
                ;;
            -d|--debug)
                DEBUG=1
                shift
                ;;
            -c|--config)
                CONFIG_FILE="$2"
                shift 2
                ;;
            --dry-run)
                DRY_RUN=1
                DEBUG=1  # Enable debug mode for dry run
                shift
                ;;
            --rollback)
                ROLLBACK_MODE=1
                shift
                ;;
            --force)
                FORCE_CONTINUE=1
                shift
                ;;
            --force-update)
                FORCE_UPDATE=1
                shift
                ;;
            --status)
                STATUS_MODE=1
                shift
                ;;
            *)
                log_msg ERROR "Unknown option: $1"
                echo "Run '$SCRIPT_NAME --help' for usage information."
                exit 1
                ;;
        esac
    done
}

# Helper function to show status for a single ISO file
show_single_iso_status() {
    local iso_file="$1"
    local checksum_file="$2"
    local iso_type="$3"  # "VMTools" or "Source"

    log_msg INFO "$iso_type ISO Path: $iso_file"
    if [[ -f "$iso_file" ]]; then
        local iso_size=$(stat -c%s "$iso_file" 2>/dev/null || echo 0)
        local iso_mtime=$(stat -c%y "$iso_file" 2>/dev/null || echo "unknown")
        log_msg INFO "$iso_type ISO Size: $(numfmt --to=iec $iso_size)"
        log_msg INFO "$iso_type ISO Modified: $iso_mtime"

        # Calculate current checksum
        local current_checksum
        current_checksum=$(calculate_checksum "$iso_file")
        log_msg INFO "Current $iso_type ISO Checksum: $current_checksum"

        # Show stored checksum if exists
        if [[ -f "$checksum_file" ]]; then
            local stored_info=$(cat "$checksum_file" 2>/dev/null || echo "")
            if [[ -n "$stored_info" ]]; then
                local stored_checksum=$(echo "$stored_info" | cut -d'|' -f1)
                local stored_timestamp=$(echo "$stored_info" | cut -d'|' -f2)
                log_msg INFO "Last Processed $iso_type ISO Checksum: $stored_checksum"
                log_msg INFO "Last Processed $iso_type ISO Time: $stored_timestamp"

                if [[ "$current_checksum" == "$stored_checksum" ]]; then
                    log_msg INFO "$iso_type ISO Status: Unchanged since last processing"
                else
                    log_msg INFO "$iso_type ISO Status: Updated since last processing"
                fi
            fi
        else
            log_msg INFO "$iso_type ISO Status: No previous processing record found"
        fi
    else
        log_msg ERROR "$iso_type ISO file not found"
    fi
}

# Show ISO status information
show_iso_status() {
    log_msg INFO "=== ISO Status Information ==="

    # VMTools ISO Status
    show_single_iso_status "$ISO_PATH" "$VMTOOLS_DIR/.vmtools_iso_checksum" "VMTools"

    log_msg INFO ""

    # Source ISO Status
    show_single_iso_status "$ISO_SRC" "$VMTOOLS_DIR/.source_iso_checksum" "Source"

    log_msg INFO ""

    # Overall status
    local needs_reprocessing=false
    if detect_vmtools_iso_update || detect_source_iso_update; then
        needs_reprocessing=true
    fi

    if [[ "$needs_reprocessing" == "true" ]]; then
        log_msg INFO "Overall Status: Reprocessing needed (ISO changes detected)"
    else
        log_msg INFO "Overall Status: No reprocessing needed"
    fi

    log_msg INFO "================================"
}

# Main process with enhanced error handling
main() {
    # Initialize script state
    SCRIPT_STATE="INITIALIZING"

    # Parse command line arguments
    parse_args "$@"

    # Handle status mode
    if [[ "${STATUS_MODE:-0}" == "1" ]]; then
        read_config
        init_paths
        show_iso_status
        exit 0
    fi

    # Handle rollback mode
    if [[ "${ROLLBACK_MODE:-0}" == "1" ]]; then
        SILENT_MODE=1  # Enable silent mode for rollback
        read_config
        init_paths
        log_msg INFO "Starting rollback process..."
        if rollback; then
            log_msg SUCCESS "Rollback completed successfully"
            exit 0
        else
            log_msg ERROR "Rollback failed"
            exit 1
        fi
    fi

    # Handle dry run mode
    if [[ "${DRY_RUN:-0}" == "1" ]]; then
        # Show banner for dry run mode
        echo "VMTools Patch Script v${SCRIPT_VERSION}"
        echo "=================================="
        log_msg DEBUG "Starting dry run mode..."

        read_config || exit 1
        init_paths
        check_env || exit 1

        # Create secure temporary directories for dry run
        create_secure_temp_dir
        TEMP_CREATED="true"

        if ! mount_and_copy_iso; then
            log_msg ERROR "Dry run failed during ISO mounting/copying"
            exit 1
        fi

        check_and_exit_if_all_exist

        log_msg INFO "Dry run completed successfully"
        exit 0
    fi

    # Normal execution
    SILENT_MODE=1  # Enable silent mode for normal execution

    # Show banner
    echo "VMTools Patch Script v${SCRIPT_VERSION}"
    echo "=================================="

    # Acquire exclusive lock
    acquire_lock

    # Main processing flow with error handling
    SCRIPT_STATE="CONFIG_READING"
    if ! read_config; then
        log_msg ERROR "Configuration reading failed"
        exit 1
    fi

    init_paths

    SCRIPT_STATE="ENV_CHECK"
    if ! check_env; then
        log_msg ERROR "Environment check failed"
        exit 1
    fi

    SCRIPT_STATE="TEMP_CREATION"
    create_secure_temp_dir
    TEMP_CREATED="true"

    # Mount and copy ISO
    if ! safe_execute "MOUNT_AND_COPY" false mount_and_copy_iso; then
        log_msg ERROR "Failed to mount and copy ISO"
        exit 1
    fi

    # Check if processing is needed
    check_and_exit_if_all_exist

    # Critical operations with automatic rollback
    if ! safe_execute "BACKUP_FILES" true backup_files; then
        log_msg ERROR "Backup creation failed"
        exit 1
    fi

    if ! safe_execute "UPDATE_VERSION" true update_version_info; then
        log_msg ERROR "Version update failed"
        exit 1
    fi

    SCRIPT_STATE="PROCESS_UPGRADE"
    if ! safe_execute "PROCESS_UPGRADE" true process_upgrade_dirs; then
        log_msg ERROR "Upgrade directory processing failed"
        exit 1
    fi

    if ! safe_execute "CREATE_ISO" true make_new_iso_and_cleanup; then
        log_msg ERROR "ISO creation failed"
        exit 1
    fi

    SCRIPT_STATE="COMPLETED"
    create_summary

    log_msg SUCCESS "VMTools patch process completed successfully"
}

# Set up signal handlers
trap cleanup_resources EXIT INT TERM HUP

# Execute main function if script is run directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
