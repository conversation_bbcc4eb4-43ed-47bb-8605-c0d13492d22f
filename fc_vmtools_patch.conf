# VMTools Patch Script Configuration File
# This file contains all the configuration parameters for the VMTools patch script

# Source ISO file path (the DS ISP ISO file)
# This should point to your DS ISP ISO file
ISO_SRC=./ds-vm-isp-20.0.2.3530.iso

# Source directory within the ISO to extract files from
# This is typically 'windows' for Windows-based DS ISP files
SRC_DIR=windows

# Directory name to create within each upgrade subdirectory
# This will contain the DS ISP files
DS_ISP_DIR=ds_isp

# VMTools installation directory
# This should point to your VMTools installation directory
VMTOOLS_DIR=/opt/patch/programfiles/vmtools

# ISO subdirectory containing the upgrade directories
# This is typically 'upgrade' in VMTools ISOs
ISO_SUBDIR=upgrade

# Log file location (optional)
# If not specified, will default to $VMTOOLS_DIR/patch_vmtools.log
LOG_FILE=/var/log/vmtools_patch.log

# Debug mode (optional, 0 or 1)
# Set to 1 to enable debug logging
DEBUG=0

# Maximum retry attempts for operations (optional)
# Default is 3
MAX_RETRIES=3

# Retry delay in seconds (optional)
# Default is 2 seconds
RETRY_DELAY=2

# Maximum parallel jobs (optional)
# Default is 4
MAX_PARALLEL_JOBS=4

# Memory limit in MB (optional)
# Default is 1024 MB
MEMORY_LIMIT_MB=1024

# Force continue despite warnings (optional, 0 or 1)
# Set to 1 to continue even if disk space is insufficient
FORCE_CONTINUE=0

# Force update even if no changes detected (optional, 0 or 1)
# Set to 1 to force reprocessing even if ISOs haven't changed
FORCE_UPDATE=0

# Silent mode (optional, 0 or 1)
# Set to 1 to reduce console output
SILENT_MODE=0

# Dry run mode (optional, 0 or 1)
# Set to 1 to show what would be done without making changes
DRY_RUN=0

# Rollback mode (optional, 0 or 1)
# Set to 1 to rollback to previous version
ROLLBACK_MODE=0

# Status mode (optional, 0 or 1)
# Set to 1 to show ISO status and processing history
STATUS_MODE=0

# Additional configuration options can be added here as needed
# All parameters are validated during script startup
