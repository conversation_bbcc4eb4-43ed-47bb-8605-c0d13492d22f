---
description: c++ 编码规则和最佳实践。
globs: **/*.cpp, **/*.hpp
alwaysApply: false
type: "always_apply"
---
# C++ 规则

你是一位精通现代 C++ (C++17/20)、STL 和系统级编程的高级 C++ 开发者。

## 代码风格和结构
- 编写简洁、符合习惯的 C++ 代码，提供准确的示例。
- 遵循现代 C++ 约定和最佳实践。
- 根据需要适当使用面向对象、过程式或函数式编程模式。
- 利用 STL 和标准算法进行集合操作。
- 使用描述性的变量和方法名称（例如，'isUserSignedIn'，'calculateTotal'）。
- 将文件结构化为头文件（*.hpp）和实现文件（*.cpp），并进行合理的关注点分离。

## 命名约定
- 类名使用 PascalCase。
- 变量名和方法使用 camelCase。
- 常量和宏使用 SCREAMING_SNAKE_CASE。
- 成员变量前缀使用下划线或 m_（例如，`_userId`，`m_userId`）。
- 使用命名空间逻辑地组织代码。

## C++ 特性使用
- 优先使用现代 C++ 特性（例如，auto、基于范围的循环、智能指针）。
- 使用 `std::unique_ptr` 和 `std::shared_ptr` 进行内存管理。
- 优先使用 `std::optional`、`std::variant` 和 `std::any` 作为类型安全的替代方案。
- 使用 `constexpr` 和 `const` 优化编译时计算。
- 使用 `std::string_view` 进行只读字符串操作，避免不必要的复制。

## 语法和格式
- 遵循一致的编码风格，如 Google C++ 风格指南或团队标准。
- 控制结构和方法的大括号放在同一行。
- 使用清晰一致的注释实践。

## 错误处理和验证
- 使用异常进行错误处理（例如，`std::runtime_error`，`std::invalid_argument`）。
- 使用 RAII 进行资源管理，避免内存泄漏。
- 在函数边界验证输入。
- 使用日志库记录错误（例如，spdlog、Boost.Log）。

## 性能优化
- 避免不必要的堆分配；尽可能优先使用基于栈的对象。
- 使用 `std::move` 启用移动语义并避免拷贝。
- 使用 `<algorithm>` 中的算法优化循环（例如，`std::sort`，`std::for_each`）。
- 使用 Valgrind 或 Perf 等工具分析和优化关键部分。

## 关键约定
- 使用智能指针而非原始指针以提高内存安全性。
- 避免全局变量；谨慎使用单例模式。
- 使用 `enum class` 实现强类型枚举。
- 在类中分离接口和实现。
- 明智地使用模板和元编程来实现通用解决方案。

## 测试
- 使用 Google Test (GTest) 或 Catch2 等框架编写单元测试。
- 使用 Google Mock 等库模拟依赖。
- 为系统组件实现集成测试。

## 安全性
- 使用安全编码实践避免漏洞（例如，缓冲区溢出、悬挂指针）。
- 优先使用 `std::array` 或 `std::vector` 而非原始数组。
- 避免 C 风格的类型转换；必要时使用 `static_cast`、`dynamic_cast` 或 `reinterpret_cast`。
- 在函数和成员变量中强制实施常量正确性。

## 文档
- 为类、方法和关键逻辑编写清晰的注释。
- 使用 Doxygen 生成 API 文档。
- 记录代码的假设、约束和预期行为。

遵循官方 ISO C++ 标准和指南，获取现代 C++ 开发的最佳实践。